#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
"""

import subprocess
import sys
import os

def check_dependencies():
    """检查依赖是否已安装"""
    required_packages = ['selenium', 'pandas', 'openpyxl', 'webdriver-manager']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """安装缺失的依赖"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 腾讯文档爬虫启动器 ===\n")
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"缺少依赖包: {', '.join(missing)}")
        install = input("是否自动安装？(y/n): ").lower().strip()
        if install == 'y':
            if not install_dependencies():
                print("安装失败，请手动运行: pip install -r requirements.txt")
                return
        else:
            print("请先安装依赖: pip install -r requirements.txt")
            return
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 简化版爬虫 (推荐)")
    print("2. 完整版爬虫")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        script = "simple_scraper.py"
    elif choice == "2":
        script = "tencent_doc_scraper.py"
    else:
        print("无效选择，使用简化版爬虫")
        script = "simple_scraper.py"
    
    # 运行脚本
    print(f"\n正在启动 {script}...")
    try:
        subprocess.run([sys.executable, script])
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"运行出错: {e}")

if __name__ == "__main__":
    main()
