#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装脚本 - 自动安装所需依赖
"""

import subprocess
import sys
import os

def install_requirements():
    """安装requirements.txt中的依赖"""
    try:
        print("正在安装Python依赖包...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装Python依赖包失败: {e}")
        return False
    return True

def check_chrome():
    """检查Chrome浏览器是否已安装"""
    try:
        # 尝试启动Chrome来检查是否安装
        if sys.platform.startswith('win'):
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
            ]
            chrome_installed = any(os.path.exists(path) for path in chrome_paths)
        elif sys.platform.startswith('darwin'):  # macOS
            chrome_installed = os.path.exists("/Applications/Google Chrome.app")
        else:  # Linux
            result = subprocess.run(['which', 'google-chrome'], capture_output=True)
            chrome_installed = result.returncode == 0
        
        if chrome_installed:
            print("✅ Chrome浏览器已安装")
            return True
        else:
            print("❌ 未检测到Chrome浏览器")
            print("请先安装Chrome浏览器: https://www.google.com/chrome/")
            return False
    except Exception as e:
        print(f"⚠️  检查Chrome浏览器时出错: {e}")
        return False

def main():
    """主安装函数"""
    print("=== 腾讯文档爬虫安装程序 ===\n")
    
    # 检查Chrome
    if not check_chrome():
        print("\n请先安装Chrome浏览器后再运行此脚本")
        return False
    
    # 安装Python依赖
    if not install_requirements():
        return False
    
    print("\n=== 安装完成 ===")
    print("现在可以运行: python tencent_doc_scraper.py")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
