#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯文档表格爬虫
用于爬取受保护的腾讯文档表格内容并导出为xlsx文件
"""

import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TencentDocScraper:
    def __init__(self, headless=False):
        """
        初始化爬虫
        :param headless: 是否使用无头模式
        """
        self.driver = None
        self.headless = headless
        self.setup_driver()
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        try:
            # 自动下载和管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            logger.info("Chrome驱动初始化成功")
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {e}")
            raise
    
    def wait_for_login(self, timeout=300):
        """
        等待用户手动登录
        :param timeout: 超时时间（秒）
        """
        logger.info("请在浏览器中完成登录，登录完成后脚本将自动继续...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查是否已经登录成功（通过检查表格内容是否加载）
                self.driver.find_element(By.CSS_SELECTOR, "table, .sheet-container, .grid-container, .excel-container")
                logger.info("检测到表格内容，登录成功")
                return True
            except NoSuchElementException:
                time.sleep(2)
                continue
        
        logger.error("登录超时")
        return False
    
    def scrape_table(self, url):
        """
        爬取表格数据
        :param url: 腾讯文档链接
        :return: 表格数据列表
        """
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 检查是否需要登录
            if "login" in self.driver.current_url.lower() or "登录" in self.driver.page_source:
                logger.info("需要登录，等待用户手动登录...")
                if not self.wait_for_login():
                    raise Exception("登录失败或超时")
            
            # 等待表格加载
            wait = WebDriverWait(self.driver, 30)
            
            # 尝试多种可能的表格选择器
            table_selectors = [
                "table",
                ".sheet-container table",
                ".grid-container",
                ".excel-container"
                "[role='grid']",
                ".luckysheet-cell-main",
                ".x-spreadsheet-table"
            ]
            
            table_element = None
            for selector in table_selectors:
                try:
                    table_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    logger.info(f"找到表格元素: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not table_element:
                raise Exception("未找到表格元素")
            
            # 等待表格内容完全加载
            time.sleep(5)
            
            # 尝试获取表格数据
            data = self.extract_table_data()
            
            if not data:
                raise Exception("未能提取到表格数据")
            
            logger.info(f"成功提取到 {len(data)} 行数据")
            return data
            
        except Exception as e:
            logger.error(f"爬取表格失败: {e}")
            raise
    
    def extract_table_data(self):
        """提取表格数据"""
        data = []
        
        # 尝试多种提取方法
        extraction_methods = [
            self._extract_from_standard_table,
            self._extract_from_grid_cells,
            self._extract_from_text_content
        ]
        
        for method in extraction_methods:
            try:
                data = method()
                if data:
                    logger.info(f"使用方法 {method.__name__} 成功提取数据")
                    break
            except Exception as e:
                logger.warning(f"方法 {method.__name__} 失败: {e}")
                continue
        
        return data
    
    def _extract_from_standard_table(self):
        """从标准HTML表格提取数据"""
        rows = self.driver.find_elements(By.CSS_SELECTOR, "table tr")
        data = []
        
        for row in rows:
            cells = row.find_elements(By.CSS_SELECTOR, "td, th")
            row_data = [cell.text.strip() for cell in cells]
            if any(row_data):  # 只添加非空行
                data.append(row_data)
        
        return data
    
    def _extract_from_grid_cells(self):
        """从网格单元格提取数据"""
        # 尝试查找各种可能的单元格选择器
        cell_selectors = [
            ".grid-cell",
            ".cell",
            "[role='gridcell']",
            ".luckysheet-cell",
            ".x-spreadsheet-cell"
        ]
        
        for selector in cell_selectors:
            cells = self.driver.find_elements(By.CSS_SELECTOR, selector)
            if cells:
                # 这里需要根据实际的DOM结构来组织数据
                # 简化处理：按行列位置组织数据
                data = self._organize_cells_by_position(cells)
                if data:
                    return data
        
        return []
    
    def _extract_from_text_content(self):
        """从页面文本内容提取数据"""
        # 获取页面所有文本，尝试解析表格结构
        page_text = self.driver.find_element(By.TAG_NAME, "body").text
        lines = page_text.split('\n')
        
        # 简单的表格数据识别
        data = []
        for line in lines:
            line = line.strip()
            if line and '\t' in line:  # 假设制表符分隔的数据
                data.append(line.split('\t'))
        
        return data if len(data) > 1 else []
    
    def _organize_cells_by_position(self, cells):
        """根据位置组织单元格数据"""
        # 这是一个简化的实现，实际可能需要根据具体的DOM结构调整
        data = []
        current_row = []
        
        for cell in cells:
            text = cell.text.strip()
            current_row.append(text)
            
            # 简单的换行检测（需要根据实际情况调整）
            if len(current_row) >= 10:  # 假设每行最多10列
                data.append(current_row)
                current_row = []
        
        if current_row:
            data.append(current_row)
        
        return data
    
    def save_to_excel(self, data, filename="tencent_doc_data.xlsx"):
        """
        保存数据到Excel文件
        :param data: 表格数据
        :param filename: 输出文件名
        """
        try:
            if not data:
                raise ValueError("没有数据可保存")
            
            # 确保所有行的列数相同
            max_cols = max(len(row) for row in data)
            normalized_data = []
            
            for row in data:
                normalized_row = row + [''] * (max_cols - len(row))
                normalized_data.append(normalized_row)
            
            # 创建DataFrame
            df = pd.DataFrame(normalized_data)
            
            # 保存到Excel
            df.to_excel(filename, index=False, header=False)
            logger.info(f"数据已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存Excel文件失败: {e}")
            raise
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")

def main():
    """主函数"""
    url = "https://doc.weixin.qq.com/sheet/e3_AbsAhAaCALACNxntU1411TryVhLJ6?scode=AMkAFAfVAGcEwKSuLoAbsAhAaCALA&tab=BB08J2"
    
    scraper = TencentDocScraper(headless=False)  # 设置为False以便手动登录
    
    try:
        # 爬取数据
        data = scraper.scrape_table(url)
        
        # 保存到Excel
        scraper.save_to_excel(data, "tencent_doc_export.xlsx")
        
        print("爬取完成！数据已保存到 tencent_doc_export.xlsx")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
