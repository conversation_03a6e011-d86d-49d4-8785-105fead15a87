# 腾讯文档表格爬虫

这是一个用于爬取受保护的腾讯文档表格内容并导出为Excel文件的Python脚本。

## 功能特点

- 🔐 支持受保护的腾讯文档（需要登录）
- 🤖 自动化浏览器操作
- 📊 多种表格数据提取方法
- 📁 导出为Excel格式(.xlsx)
- 🛡️ 反检测机制
- 📝 详细的日志记录

## 系统要求

- Python 3.7+
- Google Chrome浏览器
- 网络连接

## 安装步骤

### 方法一：自动安装（推荐）

1. 运行安装脚本：
```bash
python setup.py
```

### 方法二：手动安装

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 确保已安装Google Chrome浏览器

## 使用方法

### 基本使用

1. 运行爬虫脚本：
```bash
python tencent_doc_scraper.py
```

2. 脚本会自动打开Chrome浏览器并访问目标文档

3. 如果文档需要登录，请在浏览器中手动完成登录

4. 登录完成后，脚本会自动继续执行并提取表格数据

5. 数据将保存为 `tencent_doc_export.xlsx` 文件

### 自定义使用

```python
from tencent_doc_scraper import TencentDocScraper

# 创建爬虫实例
scraper = TencentDocScraper(headless=False)

try:
    # 爬取指定URL的表格
    url = "你的腾讯文档链接"
    data = scraper.scrape_table(url)
    
    # 保存到自定义文件名
    scraper.save_to_excel(data, "my_data.xlsx")
    
finally:
    scraper.close()
```

## 配置选项

### TencentDocScraper 参数

- `headless`: 是否使用无头模式（默认：False）
  - `True`: 后台运行，不显示浏览器窗口
  - `False`: 显示浏览器窗口，便于手动登录

### 环境变量

可以通过环境变量自定义行为：

```bash
# 设置登录等待超时时间（秒）
export LOGIN_TIMEOUT=300

# 设置页面加载等待时间（秒）
export PAGE_LOAD_TIMEOUT=30
```

## 故障排除

### 常见问题

1. **Chrome驱动问题**
   - 脚本会自动下载匹配的ChromeDriver
   - 如果仍有问题，请确保Chrome浏览器是最新版本

2. **登录超时**
   - 增加登录等待时间
   - 确保网络连接稳定
   - 手动登录时动作要快

3. **表格数据提取失败**
   - 检查文档是否真的包含表格数据
   - 尝试等待更长时间让页面完全加载
   - 检查文档权限设置

4. **Excel文件保存失败**
   - 确保有写入权限
   - 检查磁盘空间
   - 确保文件名有效

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 注意事项

⚠️ **重要提醒**

1. **合法使用**: 请确保你有权限访问目标文档
2. **频率限制**: 避免过于频繁的请求，以免被限制访问
3. **数据隐私**: 注意保护敏感数据，不要泄露登录信息
4. **版本兼容**: 腾讯文档可能会更新页面结构，导致脚本需要调整

## 技术原理

1. **Selenium WebDriver**: 模拟真实浏览器操作
2. **多重提取策略**: 
   - 标准HTML表格提取
   - 网格单元格提取
   - 文本内容解析
3. **反检测机制**: 
   - 自定义User-Agent
   - 禁用自动化标识
   - 模拟人工操作

## 依赖包说明

- `selenium`: Web自动化框架
- `pandas`: 数据处理和分析
- `openpyxl`: Excel文件读写
- `webdriver-manager`: 自动管理浏览器驱动

## 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规和网站服务条款。

## 更新日志

- v1.0.0: 初始版本，支持基本的表格爬取功能
