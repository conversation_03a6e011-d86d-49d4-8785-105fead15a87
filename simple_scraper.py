#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版腾讯文档爬虫 - 专门针对指定链接优化
"""

import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def wait_for_manual_login(driver, timeout=300):
    """等待用户手动登录"""
    logger.info("请在浏览器中完成登录，然后按回车键继续...")
    input("登录完成后按回车键继续...")
    return True

def extract_table_data(driver):
    """提取表格数据"""
    logger.info("正在提取表格数据...")
    
    # 等待页面完全加载
    time.sleep(10)
    
    data = []
    
    try:
        # 方法1: 尝试获取所有可见的文本内容
        logger.info("尝试方法1: 获取页面文本内容")
        
        # 查找包含数据的元素
        possible_selectors = [
            " .excel-container",
            "table tr",
            ".luckysheet-cell-main .luckysheet-cell",
            ".x-spreadsheet-table .x-spreadsheet-cell",
            "[role='gridcell']",
            ".grid-cell",
            ".cell"
        ]
        
        for selector in possible_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"找到 {len(elements)} 个元素，使用选择器: {selector}")
                    
                    if "tr" in selector:
                        # 处理表格行
                        for row_element in elements:
                            cells = row_element.find_elements(By.CSS_SELECTOR, "td, th")
                            if cells:
                                row_data = [cell.text.strip() for cell in cells]
                                if any(row_data):  # 只添加非空行
                                    data.append(row_data)
                    else:
                        # 处理单个单元格
                        row_data = []
                        for element in elements:
                            text = element.text.strip()
                            if text:
                                row_data.append(text)
                        
                        # 简单的行分割（每10个单元格一行）
                        if row_data:
                            for i in range(0, len(row_data), 10):
                                data.append(row_data[i:i+10])
                    
                    if data:
                        break
            except Exception as e:
                logger.warning(f"选择器 {selector} 失败: {e}")
                continue
        
        # 方法2: 如果上面的方法都失败，尝试获取页面的所有文本
        if not data:
            logger.info("尝试方法2: 解析页面文本")
            page_text = driver.find_element(By.TAG_NAME, "body").text
            lines = page_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if line and any(char.isalnum() for char in line):
                    # 尝试按制表符或多个空格分割
                    if '\t' in line:
                        data.append(line.split('\t'))
                    elif '  ' in line:  # 多个空格
                        data.append([item.strip() for item in line.split('  ') if item.strip()])
                    else:
                        data.append([line])
        
        # 方法3: 截图并提示用户手动复制
        if not data:
            logger.info("自动提取失败，请手动复制数据")
            print("\n" + "="*50)
            print("自动提取失败，请按以下步骤手动操作：")
            print("1. 在浏览器中选择所有表格数据")
            print("2. 复制数据 (Ctrl+C)")
            print("3. 将数据粘贴到Excel或文本文件中")
            print("="*50)
            
            # 保持浏览器打开一段时间
            input("完成手动复制后按回车键关闭浏览器...")
        
    except Exception as e:
        logger.error(f"提取数据时出错: {e}")
    
    return data

def save_to_excel(data, filename="tencent_doc_export.xlsx"):
    """保存数据到Excel文件"""
    if not data:
        logger.warning("没有数据可保存")
        return False
    
    try:
        # 确保所有行的列数相同
        max_cols = max(len(row) for row in data) if data else 0
        normalized_data = []
        
        for row in data:
            normalized_row = row + [''] * (max_cols - len(row))
            normalized_data.append(normalized_row)
        
        # 创建DataFrame
        df = pd.DataFrame(normalized_data)
        
        # 保存到Excel
        df.to_excel(filename, index=False, header=False)
        logger.info(f"数据已保存到: {filename}")
        print(f"✅ 数据已保存到: {filename}")
        return True
        
    except Exception as e:
        logger.error(f"保存Excel文件失败: {e}")
        return False

def main():
    """主函数"""
    url = "https://doc.weixin.qq.com/sheet/e3_AbsAhAaCALACNxntU1411TryVhLJ6?scode=AMkAFAfVAGcEwKSuLoAbsAhAaCALA&tab=BB08J2"
    
    print("=== 腾讯文档表格爬虫 ===")
    print(f"目标链接: {url}")
    print()
    
    driver = None
    try:
        # 设置浏览器
        logger.info("正在启动Chrome浏览器...")
        driver = setup_driver()
        
        # 访问页面
        logger.info("正在访问腾讯文档...")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 检查是否需要登录
        current_url = driver.current_url.lower()
        page_source = driver.page_source.lower()
        
        if "login" in current_url or "登录" in page_source or "sign" in current_url:
            logger.info("检测到需要登录")
            wait_for_manual_login(driver)
            time.sleep(5)  # 登录后等待页面加载
        
        # 提取数据
        data = extract_table_data(driver)
        
        if data:
            # 保存数据
            success = save_to_excel(data)
            if success:
                print(f"\n🎉 成功提取 {len(data)} 行数据并保存到Excel文件！")
            else:
                print("\n❌ 保存文件失败")
        else:
            print("\n⚠️  未能提取到表格数据，请检查文档链接或手动复制数据")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
    
    finally:
        if driver:
            input("\n按回车键关闭浏览器...")
            driver.quit()
            logger.info("浏览器已关闭")

if __name__ == "__main__":
    main()
